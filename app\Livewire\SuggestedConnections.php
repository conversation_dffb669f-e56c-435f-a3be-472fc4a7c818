<?php

namespace App\Livewire;

use App\Models\User;
use App\Models\Organization;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Collection;

class SuggestedConnections extends Component
{
    public $suggestedUsers = [];
    public $isLoading = true;
    public $refreshKey = 0;

    public function mount()
    {
        $this->loadSuggestedUsers();
    }

    public function loadSuggestedUsers()
    {
        if (!Auth::check()) {
            $this->suggestedUsers = [];
            $this->isLoading = false;
            return;
        }

        $currentUser = Auth::user();
        $allSuggestions = collect();
        $seenUserIds = collect();

        // Get users already being followed to exclude them
        $followingIds = $currentUser->following()->pluck('users.id')->toArray();
        $followingIds[] = $currentUser->id; // Exclude self

        // 1. Find users from same organizations
        $organizationSuggestions = $this->getUsersFromSameOrganizations($currentUser, $followingIds);
        foreach ($organizationSuggestions as $user) {
            if (!$seenUserIds->contains($user->id)) {
                $allSuggestions->push($user);
                $seenUserIds->push($user->id);
            }
        }

        // 2. Find users from same campus and course
        $academicSuggestions = $this->getUsersFromSameAcademicBackground($currentUser, $followingIds);
        foreach ($academicSuggestions as $user) {
            if (!$seenUserIds->contains($user->id) && $allSuggestions->count() < 5) {
                $allSuggestions->push($user);
                $seenUserIds->push($user->id);
            }
        }

        // 3. Find users with similar skills/interests
        $skillsSuggestions = $this->getUsersWithSimilarSkills($currentUser, $followingIds);
        foreach ($skillsSuggestions as $user) {
            if (!$seenUserIds->contains($user->id) && $allSuggestions->count() < 5) {
                $allSuggestions->push($user);
                $seenUserIds->push($user->id);
            }
        }

        // 4. Find popular users (fallback)
        if ($allSuggestions->count() < 5) {
            $excludeIds = array_merge($followingIds, $seenUserIds->toArray());
            $popularSuggestions = $this->getPopularUsers($currentUser, $excludeIds, 5 - $allSuggestions->count());
            foreach ($popularSuggestions as $user) {
                if (!$seenUserIds->contains($user->id) && $allSuggestions->count() < 5) {
                    $allSuggestions->push($user);
                    $seenUserIds->push($user->id);
                }
            }
        }

        // Limit to 5 users and format the data
        $this->suggestedUsers = $allSuggestions
            ->take(5)
            ->map(function ($user) use ($currentUser) {
                return [
                    'user' => $user,
                    'shared_attributes' => $this->getSharedAttributes($currentUser, $user),
                    'is_following' => false // Will be updated by UserFollower component
                ];
            })
            ->toArray();

        $this->isLoading = false;
    }

    private function getUsersFromSameOrganizations(User $currentUser, array $excludeIds): Collection
    {
        $userOrganizations = $currentUser->organizations()->pluck('organizations.id');
        
        if ($userOrganizations->isEmpty()) {
            return collect();
        }

        return User::whereHas('organizations', function ($query) use ($userOrganizations) {
                $query->whereIn('organizations.id', $userOrganizations);
            })
            ->whereNotIn('id', $excludeIds)
            ->with(['organizations'])
            ->limit(10)
            ->get();
    }

    private function getUsersFromSameAcademicBackground(User $currentUser, array $excludeIds): Collection
    {
        $users = collect();

        // Same campus users
        if ($currentUser->campus) {
            $campusUsers = User::whereNotIn('id', $excludeIds)
                ->where('campus', $currentUser->campus)
                ->limit(5)
                ->get();
            $users = $users->merge($campusUsers);
        }

        // Same course program users
        if ($currentUser->course_program) {
            $courseUsers = User::whereNotIn('id', $excludeIds)
                ->where('course_program', $currentUser->course_program)
                ->limit(5)
                ->get();
            $users = $users->merge($courseUsers);
        }

        // Same college department users
        if ($currentUser->college_department) {
            $deptUsers = User::whereNotIn('id', $excludeIds)
                ->where('college_department', $currentUser->college_department)
                ->limit(5)
                ->get();
            $users = $users->merge($deptUsers);
        }

        return $users->unique('id')->take(10);
    }

    private function getUsersWithSimilarSkills(User $currentUser, array $excludeIds): Collection
    {
        $userSkills = $currentUser->skills_interests ?? [];
        
        if (empty($userSkills)) {
            return collect();
        }

        return User::whereNotIn('id', $excludeIds)
            ->whereNotNull('skills_interests')
            ->get()
            ->filter(function ($user) use ($userSkills) {
                $otherSkills = $user->skills_interests ?? [];
                $commonSkills = array_intersect($userSkills, $otherSkills);
                return count($commonSkills) > 0;
            })
            ->take(10);
    }

    private function getPopularUsers(User $currentUser, array $excludeIds, int $limit): Collection
    {
        return User::whereNotIn('id', $excludeIds)
            ->withCount('followers')
            ->orderBy('followers_count', 'desc')
            ->limit($limit)
            ->get();
    }

    private function getSharedAttributes(User $currentUser, User $otherUser): array
    {
        $shared = [];

        // Check for shared organizations
        $currentUserOrgs = $currentUser->organizations()->pluck('name')->toArray();
        $otherUserOrgs = $otherUser->organizations()->pluck('name')->toArray();
        $sharedOrgs = array_intersect($currentUserOrgs, $otherUserOrgs);
        
        if (!empty($sharedOrgs)) {
            $shared[] = 'Member of ' . implode(', ', array_slice($sharedOrgs, 0, 2));
        }

        // Check for same campus
        if ($currentUser->campus && $currentUser->campus === $otherUser->campus) {
            $shared[] = 'Same campus: ' . $currentUser->campus;
        }

        // Check for same course
        if ($currentUser->course_program && $currentUser->course_program === $otherUser->course_program) {
            $shared[] = 'Same course: ' . $currentUser->course_program;
        }

        // Check for similar skills
        $currentSkills = $currentUser->skills_interests ?? [];
        $otherSkills = $otherUser->skills_interests ?? [];
        $commonSkills = array_intersect($currentSkills, $otherSkills);
        
        if (!empty($commonSkills)) {
            $shared[] = 'Similar interests: ' . implode(', ', array_slice($commonSkills, 0, 3));
        }

        return $shared;
    }

    public function refreshSuggestions()
    {
        $this->isLoading = true;
        $this->suggestedUsers = [];
        $this->refreshKey++; // Increment to force component re-render

        // Force a re-render by dispatching a browser event
        $this->dispatch('refreshed');

        $this->loadSuggestedUsers();
    }

    /**
     * Listen for follow/unfollow events to refresh suggestions
     */
    protected $listeners = ['userFollowed' => 'handleUserFollowed', 'userUnfollowed' => 'handleUserUnfollowed'];

    public function handleUserFollowed($userId)
    {
        // Remove the followed user from suggestions
        $this->suggestedUsers = array_filter($this->suggestedUsers, function($suggestion) use ($userId) {
            return $suggestion['user']->id !== $userId;
        });

        // If we have less than 5 suggestions, load more
        if (count($this->suggestedUsers) < 5) {
            $this->loadSuggestedUsers();
        }
    }

    public function handleUserUnfollowed($userId)
    {
        // Optionally refresh suggestions when someone is unfollowed
        // This could add them back to the suggestions
        $this->loadSuggestedUsers();
    }

    public function render()
    {
        return view('livewire.suggested-connections');
    }
}
